import axios from '@/services/axios'
import router from "@/router/index";
import { jwtDecode } from 'jwt-decode';

// Constantes para o sistema de sessão
const SESSION_KEYS = {
    IS_AUTHENTICATED: 'isAuthenticated',
    TOKEN: 'token',
    REMEMBER_DEVICE: 'rememberDevice',
    LAST_ACTIVITY: 'lastActivity',
    SESSION_START: 'sessionStart'
};

const INACTIVITY_LIMIT_DAYS = 30;
const INACTIVITY_LIMIT_MS = INACTIVITY_LIMIT_DAYS * 24 * 60 * 60 * 1000;

// Gerenciador de sessão
const SessionManager = {
    // Verifica se deve usar localStorage ou sessionStorage
    getStorage() {
        const rememberDevice = localStorage.getItem(SESSION_KEYS.REMEMBER_DEVICE) === 'true';
        return rememberDevice ? localStorage : sessionStorage;
    },

    // Salva dados da sessão
    setSessionData(key, value) {
        const storage = this.getStorage();
        storage.setItem(key, value);

        // Sempre salvar a preferência de "lembrar dispositivo" no localStorage
        if (key === SESSION_KEYS.REMEMBER_DEVICE) {
            localStorage.setItem(key, value);
        }
    },

    // Recupera dados da sessão
    getSessionData(key) {
        // Para alguns dados críticos, verificar ambos os storages
        if (key === SESSION_KEYS.REMEMBER_DEVICE) {
            return localStorage.getItem(key);
        }

        const storage = this.getStorage();
        let value = storage.getItem(key);

        // Fallback: se não encontrar no storage atual, verificar o outro
        if (!value && key !== SESSION_KEYS.REMEMBER_DEVICE) {
            const alternativeStorage = storage === localStorage ? sessionStorage : localStorage;
            value = alternativeStorage.getItem(key);
        }

        return value;
    },

    // Remove dados da sessão
    removeSessionData(key) {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
    },

    // Limpa toda a sessão
    clearSession() {
        Object.values(SESSION_KEYS).forEach(key => {
            this.removeSessionData(key);
        });
    },

    // Atualiza timestamp de última atividade
    updateActivity() {
        const now = Date.now();
        this.setSessionData(SESSION_KEYS.LAST_ACTIVITY, now.toString());
    },

    // Verifica se a sessão expirou por inatividade
    isSessionExpiredByInactivity() {
        const lastActivity = this.getSessionData(SESSION_KEYS.LAST_ACTIVITY);
        if (!lastActivity) return false;

        const now = Date.now();
        const lastActivityTime = parseInt(lastActivity);
        return (now - lastActivityTime) > INACTIVITY_LIMIT_MS;
    },

    // Verifica se é uma nova janela/aba (para usuários sem "lembrar dispositivo")
    isNewWindow() {
        const rememberDevice = this.getSessionData(SESSION_KEYS.REMEMBER_DEVICE) === 'true';
        if (rememberDevice) return false;

        // Se não quer lembrar o dispositivo, verificar se há dados apenas no localStorage
        // mas não no sessionStorage (indicando nova janela)
        const hasLocalAuth = localStorage.getItem(SESSION_KEYS.IS_AUTHENTICATED) === 'true';
        const hasSessionAuth = sessionStorage.getItem(SESSION_KEYS.IS_AUTHENTICATED) === 'true';

        return hasLocalAuth && !hasSessionAuth;
    },

    // Inicializa a sessão
    initializeSession(rememberDevice = false) {
        this.setSessionData(SESSION_KEYS.REMEMBER_DEVICE, rememberDevice.toString());
        this.setSessionData(SESSION_KEYS.SESSION_START, Date.now().toString());
        this.updateActivity();
    }
};

function isAuthenticated() {
    const authStatus = SessionManager.getSessionData(SESSION_KEYS.IS_AUTHENTICATED) === 'true';

    if (!authStatus) return false;

    // Verificar se é uma nova janela e o usuário não quer manter conectado
    if (SessionManager.isNewWindow()) {
        return false;
    }

    // Verificar se a sessão expirou por inatividade
    if (SessionManager.isSessionExpiredByInactivity()) {
        SessionManager.clearSession();
        return false;
    }

    // Atualizar atividade se estiver autenticado
    SessionManager.updateActivity();
    return true;
}

function isSystemAdmin() {
    const decoded = decodedToken();
    if (!decoded) return false;

    return typeof decoded.system_admin === 'boolean' ? decoded.system_admin : decoded.system_admin === 1;
}

function getClinica() {
    const decoded = decodedToken();
    if (!decoded) return null;
    return decoded.clinica;
}

function getAuthToken() {
    return SessionManager.getSessionData(SESSION_KEYS.TOKEN);
}

function decodedToken() {
    const token = getAuthToken();
    if (!token) return null;

    try {
        const decoded = jwtDecode(token);
        return decoded;
    } catch (error) {
        console.error('Erro ao decodificar token:', error);
        return null;
    }
}

async function login(credentials, rememberDevice = false) {
    try {
        const { username, password } = credentials

        const response = await axios.post('/auth/login', {
            username, password
        });

        if (!response || !response.data || !response.data.access_token)
            return false

        const data = response.data

        // Inicializar a sessão com a preferência do usuário
        SessionManager.initializeSession(rememberDevice);

        // Configurar o token
        axios.refreshToken(data.access_token)
        SessionManager.setSessionData(SESSION_KEYS.IS_AUTHENTICATED, 'true');
        SessionManager.setSessionData(SESSION_KEYS.TOKEN, data.access_token);

        return true

    } catch (error) {
        console.error('Erro ao realizar login:', error);
    }

    return false
}

async function logout(callback = null) {
    try {
        await axios.post('/auth/logout')
        if (callback) callback()

        SessionManager.clearSession()
        router.push('/entrar')

        return true

    } catch (error) {
        console.error('Erro ao realizar logout:', error);
        return false
    }
}

async function refreshAuth(options = {}) {
    // Se não está autenticado, retorna null imediatamente
    if (!isAuthenticated()) {
        return null;
    }

    // Verificar se o token existe e é válido
    const token = decodedToken();
    if (!token) {
        // Token inválido, limpar autenticação
        SessionManager.clearSession();
        return false;
    }

    // Se não é forçado e o token ainda é válido, retorna true
    if (!options?.force && token.exp > Math.floor(Date.now() / 1000)) {
        // Atualizar atividade quando verificamos a autenticação
        SessionManager.updateActivity();
        return true;
    }

    try {
        const response = await axios.post('/auth/refresh');

        if (!response || !response.data || !response.data.access_token) {
            // Falha no refresh, limpar autenticação
            SessionManager.clearSession();
            return false;
        }

        const data = response.data;
        axios.refreshToken(data.access_token);
        SessionManager.setSessionData(SESSION_KEYS.TOKEN, data.access_token);
        SessionManager.updateActivity();

        return true;

    } catch (error) {
        console.error('Erro ao realizar refresh do token:', error);
        // Em caso de erro, limpar autenticação
        SessionManager.clearSession();
        return false;
    }
}

async function updateProfile(userData) {
    try {
        const response = await axios.patch('/profile', userData);

        if (!response || !response.data || response.data.status === 'error')
            return false;

        return response.data;

    } catch (error) {
        console.error('Erro ao atualizar perfil:', error);
        return false;
    }
}

export default {
    login,
    logout,
    decodedToken,
    getAuthToken,
    isSystemAdmin,
    getClinica,
    isAuthenticated,
    refreshAuth,
    updateProfile,
    SessionManager, // Exportar para uso em outros lugares se necessário
}
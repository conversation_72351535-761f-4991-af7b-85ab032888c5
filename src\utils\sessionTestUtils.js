/**
 * Utilitários para testar o sistema de sessão
 * Apenas para desenvolvimento/debug
 */

// Função para obter usuariosService sem dependência circular
const getUsuariosService = () => {
  try {
    return require('@/services/usuariosService').default;
  } catch (error) {
    console.error('Erro ao importar usuariosService:', error);
    return null;
  }
};

export const SessionTestUtils = {
  // Simular inatividade (definir última atividade para 31 dias atrás)
  simulateInactivity() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return;

    const thirtyOneDaysAgo = Date.now() - (31 * 24 * 60 * 60 * 1000);
    usuariosService.SessionManager.setSessionData('lastActivity', thirtyOneDaysAgo.toString());
    console.log('✅ Inatividade simulada (31 dias atrás)');
  },

  // Resetar atividade para agora
  resetActivity() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return;

    usuariosService.SessionManager.updateActivity();
    console.log('✅ Atividade resetada para agora');
  },

  // Verificar status da sessão
  checkSessionStatus() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) {
      console.error('❌ usuariosService não disponível');
      return null;
    }

    const sessionManager = usuariosService.SessionManager;
    const rememberDevice = sessionManager.getSessionData('rememberDevice') === 'true';
    const lastActivity = sessionManager.getSessionData('lastActivity');
    const isAuthenticated = usuariosService.isAuthenticated();
    const isExpired = sessionManager.isSessionExpiredByInactivity();
    const isNewBrowserSession = sessionManager.isNewBrowserSession();
    const hasSessionAuth = sessionStorage.getItem('isAuthenticated') === 'true';
    const hasLocalAuth = localStorage.getItem('isAuthenticated') === 'true';

    console.log('📊 Status da Sessão:');
    console.log('  - Lembrar dispositivo:', rememberDevice);
    console.log('  - Última atividade:', lastActivity ? new Date(parseInt(lastActivity)).toLocaleString() : 'N/A');
    console.log('  - Autenticado:', isAuthenticated);
    console.log('  - Expirado por inatividade:', isExpired);
    console.log('  - Nova sessão do navegador:', isNewBrowserSession);
    console.log('  - Auth no sessionStorage:', hasSessionAuth);
    console.log('  - Auth no localStorage:', hasLocalAuth);
    console.log('  - Storage usado:', rememberDevice ? 'localStorage' : 'sessionStorage');

    return {
      rememberDevice,
      lastActivity,
      isAuthenticated,
      isExpired,
      isNewBrowserSession,
      hasSessionAuth,
      hasLocalAuth,
      storageType: rememberDevice ? 'localStorage' : 'sessionStorage'
    };
  },

  // Limpar sessão completamente
  clearSession() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return;

    usuariosService.SessionManager.clearSession();
    console.log('🧹 Sessão limpa completamente');
  },

  // Simular nova sessão do navegador (similar ao comportamento do PHP)
  simulateNewBrowserSession() {
    // Limpar apenas o sessionStorage (simula fechar e reabrir o navegador)
    sessionStorage.clear();

    console.log('🪟 Nova sessão do navegador simulada (sessionStorage limpo, localStorage mantido)');
    console.log('   Isso simula o comportamento de fechar e reabrir o navegador');
  },

  // Simular nova aba (sessionStorage é compartilhado entre abas)
  simulateNewTab() {
    // Nova aba não precisa simular nada, pois sessionStorage é compartilhado
    console.log('📑 Nova aba: sessionStorage é compartilhado, deve manter logado');
  },

  // Testar cenário: usuário COM "lembrar dispositivo"
  testRememberDeviceScenario() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return false;

    console.log('\n🧪 Testando cenário: COM "lembrar dispositivo"');

    // Simular login com lembrar dispositivo
    usuariosService.SessionManager.initializeSession(true);
    usuariosService.SessionManager.setSessionData('isAuthenticated', 'true');

    console.log('1. Login realizado com "lembrar dispositivo" = true');
    this.checkSessionStatus();

    // Simular nova aba (deve manter logado)
    this.simulateNewTab();
    console.log('2. Após abrir nova aba:');
    this.checkSessionStatus();

    return usuariosService.isAuthenticated();
  },

  // Testar cenário: usuário SEM "lembrar dispositivo"
  testDontRememberDeviceScenario() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return false;

    console.log('\n🧪 Testando cenário: SEM "lembrar dispositivo"');

    // Simular login sem lembrar dispositivo
    usuariosService.SessionManager.initializeSession(false);
    usuariosService.SessionManager.setSessionData('isAuthenticated', 'true');

    console.log('1. Login realizado com "lembrar dispositivo" = false');
    this.checkSessionStatus();

    // Simular nova sessão do navegador (deve deslogar)
    this.simulateNewBrowserSession();
    console.log('2. Após nova sessão do navegador:');
    this.checkSessionStatus();

    return usuariosService.isAuthenticated();
  },

  // Testar cenário: inatividade de 30+ dias
  testInactivityScenario() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return false;

    console.log('\n🧪 Testando cenário: Inatividade de 30+ dias');

    // Simular login
    usuariosService.SessionManager.initializeSession(true);
    usuariosService.SessionManager.setSessionData('isAuthenticated', 'true');

    console.log('1. Login realizado:');
    this.checkSessionStatus();

    // Simular inatividade
    this.simulateInactivity();
    console.log('2. Após 31 dias de inatividade:');
    this.checkSessionStatus();

    return usuariosService.isAuthenticated();
  },

  // Testar comportamento completo: nova aba vs nova sessão
  testCompleteSessionBehavior() {
    console.log('\n🧪 Teste Completo: Comportamento de Sessão (similar ao PHP)');

    // Teste 1: COM "lembrar dispositivo"
    console.log('\n--- PARTE 1: COM "lembrar dispositivo" ---');
    this.testRememberDeviceScenario();

    // Teste 2: SEM "lembrar dispositivo" - nova aba
    console.log('\n--- PARTE 2: SEM "lembrar dispositivo" - Nova Aba ---');
    this.clearSession();
    const usuariosService = getUsuariosService();
    if (usuariosService) {
      usuariosService.SessionManager.initializeSession(false);
      usuariosService.SessionManager.setSessionData('isAuthenticated', 'true');
      console.log('Login realizado sem "lembrar dispositivo"');
      this.checkSessionStatus();

      this.simulateNewTab();
      console.log('Após nova aba (deve manter logado):');
      this.checkSessionStatus();
      console.log('Resultado:', usuariosService.isAuthenticated() ? '✅ Manteve logado' : '❌ Deslogou');
    }

    // Teste 3: SEM "lembrar dispositivo" - nova sessão do navegador
    console.log('\n--- PARTE 3: SEM "lembrar dispositivo" - Nova Sessão do Navegador ---');
    if (usuariosService) {
      usuariosService.SessionManager.initializeSession(false);
      usuariosService.SessionManager.setSessionData('isAuthenticated', 'true');
      console.log('Login realizado sem "lembrar dispositivo"');
      this.checkSessionStatus();

      this.simulateNewBrowserSession();
      console.log('Após nova sessão do navegador (deve deslogar):');
      this.checkSessionStatus();
      console.log('Resultado:', usuariosService.isAuthenticated() ? '❌ Manteve logado' : '✅ Deslogou corretamente');
    }

    console.log('\n🎯 Resumo do comportamento esperado:');
    console.log('  - COM "lembrar": sempre mantém logado');
    console.log('  - SEM "lembrar" + nova aba: mantém logado');
    console.log('  - SEM "lembrar" + nova sessão: desloga');
  }
};

// Disponibilizar globalmente para debug no console
if (typeof window !== 'undefined') {
  window.SessionTestUtils = SessionTestUtils;
  console.log('🔧 SessionTestUtils disponível globalmente. Use window.SessionTestUtils no console.');
}

export default SessionTestUtils;

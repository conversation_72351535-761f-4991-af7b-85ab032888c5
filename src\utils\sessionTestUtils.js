/**
 * Utilitários para testar o sistema de sessão
 * Apenas para desenvolvimento/debug
 */

// Função para obter usuariosService sem dependência circular
const getUsuariosService = () => {
  try {
    return require('@/services/usuariosService').default;
  } catch (error) {
    console.error('Erro ao importar usuariosService:', error);
    return null;
  }
};

export const SessionTestUtils = {
  // Simular inatividade (definir última atividade para 31 dias atrás)
  simulateInactivity() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return;

    const thirtyOneDaysAgo = Date.now() - (31 * 24 * 60 * 60 * 1000);
    usuariosService.SessionManager.setSessionData('lastActivity', thirtyOneDaysAgo.toString());
    console.log('✅ Inatividade simulada (31 dias atrás)');
  },

  // Resetar atividade para agora
  resetActivity() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return;

    usuariosService.SessionManager.updateActivity();
    console.log('✅ Atividade resetada para agora');
  },

  // Verificar status da sessão
  checkSessionStatus() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) {
      console.error('❌ usuariosService não disponível');
      return null;
    }

    const sessionManager = usuariosService.SessionManager;
    const rememberDevice = sessionManager.getSessionData('rememberDevice') === 'true';
    const lastActivity = sessionManager.getSessionData('lastActivity');
    const isAuthenticated = usuariosService.isAuthenticated();
    const isExpired = sessionManager.isSessionExpiredByInactivity();
    const isNewWindow = sessionManager.isNewWindow();

    console.log('📊 Status da Sessão:');
    console.log('  - Lembrar dispositivo:', rememberDevice);
    console.log('  - Última atividade:', lastActivity ? new Date(parseInt(lastActivity)).toLocaleString() : 'N/A');
    console.log('  - Autenticado:', isAuthenticated);
    console.log('  - Expirado por inatividade:', isExpired);
    console.log('  - Nova janela:', isNewWindow);
    console.log('  - Storage usado:', rememberDevice ? 'localStorage' : 'sessionStorage');

    return {
      rememberDevice,
      lastActivity,
      isAuthenticated,
      isExpired,
      isNewWindow,
      storageType: rememberDevice ? 'localStorage' : 'sessionStorage'
    };
  },

  // Limpar sessão completamente
  clearSession() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return;

    usuariosService.SessionManager.clearSession();
    console.log('🧹 Sessão limpa completamente');
  },

  // Simular nova janela (limpar sessionStorage mas manter localStorage)
  simulateNewWindow() {
    // Salvar dados do localStorage
    const localData = {};
    Object.keys(localStorage).forEach(key => {
      localData[key] = localStorage.getItem(key);
    });

    // Limpar sessionStorage
    sessionStorage.clear();

    console.log('🪟 Nova janela simulada (sessionStorage limpo, localStorage mantido)');
    return localData;
  },

  // Testar cenário: usuário COM "lembrar dispositivo"
  testRememberDeviceScenario() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return false;

    console.log('\n🧪 Testando cenário: COM "lembrar dispositivo"');

    // Simular login com lembrar dispositivo
    usuariosService.SessionManager.initializeSession(true);
    usuariosService.SessionManager.setSessionData('isAuthenticated', 'true');

    console.log('1. Login realizado com "lembrar dispositivo" = true');
    this.checkSessionStatus();

    // Simular nova janela
    this.simulateNewWindow();
    console.log('2. Após abrir nova janela:');
    this.checkSessionStatus();

    return usuariosService.isAuthenticated();
  },

  // Testar cenário: usuário SEM "lembrar dispositivo"
  testDontRememberDeviceScenario() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return false;

    console.log('\n🧪 Testando cenário: SEM "lembrar dispositivo"');

    // Simular login sem lembrar dispositivo
    usuariosService.SessionManager.initializeSession(false);
    usuariosService.SessionManager.setSessionData('isAuthenticated', 'true');

    console.log('1. Login realizado com "lembrar dispositivo" = false');
    this.checkSessionStatus();

    // Simular nova janela
    this.simulateNewWindow();
    console.log('2. Após abrir nova janela:');
    this.checkSessionStatus();

    return usuariosService.isAuthenticated();
  },

  // Testar cenário: inatividade de 30+ dias
  testInactivityScenario() {
    const usuariosService = getUsuariosService();
    if (!usuariosService) return false;

    console.log('\n🧪 Testando cenário: Inatividade de 30+ dias');

    // Simular login
    usuariosService.SessionManager.initializeSession(true);
    usuariosService.SessionManager.setSessionData('isAuthenticated', 'true');

    console.log('1. Login realizado:');
    this.checkSessionStatus();

    // Simular inatividade
    this.simulateInactivity();
    console.log('2. Após 31 dias de inatividade:');
    this.checkSessionStatus();

    return usuariosService.isAuthenticated();
  }
};

// Disponibilizar globalmente para debug no console
if (typeof window !== 'undefined') {
  window.SessionTestUtils = SessionTestUtils;
  console.log('🔧 SessionTestUtils disponível globalmente. Use window.SessionTestUtils no console.');
}

export default SessionTestUtils;

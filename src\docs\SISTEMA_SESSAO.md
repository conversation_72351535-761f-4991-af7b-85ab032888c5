# Sistema de Expiração de Login Inteligente

## Visão Geral

O sistema implementa um controle inteligente de expiração de login baseado na opção "Manter este dispositivo conectado" e na atividade do usuário.

## Como Funciona

### 1. Opção "Manter este dispositivo conectado"

**✅ MARCADO (true):**
- Dados da sessão são salvos no `localStorage`
- Login persiste mesmo ao fechar e reabrir o navegador
- Sessão só expira por inatividade (30 dias) ou logout manual

**❌ NÃO MARCADO (false) - Comportamento similar ao PHP:**
- Dados da sessão são salvos no `sessionStorage` + ID de sessão do navegador
- Login persiste entre abas da mesma sessão do navegador
- Login expira ao fechar e reabrir o navegador (nova sessão)
- Sessão também expira por inatividade (30 dias)

### 2. Verificação de Inatividade

- Sistema rastreia a última atividade do usuário
- Atividade é atualizada a cada 30 segundos (throttled)
- Se o usuário ficar 30 dias sem atividade, a sessão expira automaticamente
- Atividades monitoradas: cliques, movimento do mouse, teclas, scroll, touch

### 3. Verificação ao Carregar o App

O sistema verifica automaticamente:
1. Se há uma sessão válida
2. Se é uma nova sessão do navegador (para usuários sem "lembrar dispositivo")
3. Se a sessão expirou por inatividade
4. Se o token JWT ainda é válido

### 4. Sistema de ID de Sessão do Navegador

- Cada login gera um ID único de sessão
- Para usuários SEM "lembrar dispositivo":
  - ID é salvo no `sessionStorage` (expira ao fechar navegador)
  - ID também é salvo no `localStorage` para comparação
  - Se não há ID no `sessionStorage`, mas há no `localStorage` = nova sessão
- Para usuários COM "lembrar dispositivo":
  - Sistema ignora verificação de sessão do navegador

## Arquivos Modificados

### `src/services/usuariosService.js`
- Adicionado `SessionManager` para gerenciar localStorage vs sessionStorage
- Atualizada função `isAuthenticated()` com verificações inteligentes
- Modificada função `login()` para aceitar parâmetro `rememberDevice`
- Atualizada função `refreshAuth()` para usar o novo sistema

### `src/views/Entrar.vue`
- Modificado `submitLogin()` para passar a opção `rememberDevice`

### `src/composables/useActivityTracker.js`
- Novo composable para rastrear atividade do usuário
- Throttling de 30 segundos para otimizar performance

### `src/App.vue`
- Integrado o rastreador de atividade
- Inicialização automática quando usuário está autenticado

### `src/router/index.js`
- Adicionados comentários explicativos
- Sistema já funcionava corretamente com as novas verificações

### `src/services/axios.js`
- Atualizado para usar o SessionManager
- Compatibilidade com localStorage e sessionStorage

## Como Testar

### No Console do Navegador (Desenvolvimento)

```javascript
// Verificar status atual da sessão
window.SessionTestUtils.checkSessionStatus()

// Testar cenário COM "lembrar dispositivo"
window.SessionTestUtils.testRememberDeviceScenario()

// Testar cenário SEM "lembrar dispositivo"  
window.SessionTestUtils.testDontRememberDeviceScenario()

// Testar inatividade de 30+ dias
window.SessionTestUtils.testInactivityScenario()

// Simular inatividade
window.SessionTestUtils.simulateInactivity()

// Resetar atividade
window.SessionTestUtils.resetActivity()

// Limpar sessão
window.SessionTestUtils.clearSession()
```

### Cenários de Teste Manual

1. **Login com "Manter conectado" MARCADO:**
   - Fazer login marcando a opção
   - Abrir nova aba → deve manter logado
   - Fechar navegador e reabrir → deve manter logado

2. **Login com "Manter conectado" DESMARCADO:**
   - Fazer login sem marcar a opção
   - Abrir nova aba → deve manter logado (mesma sessão)
   - Fechar navegador e reabrir → deve deslogar (nova sessão)

3. **Teste de Inatividade:**
   - Fazer login
   - Usar `SessionTestUtils.simulateInactivity()` no console
   - Tentar navegar ou recarregar a página
   - Verificar se foi deslogado

4. **Teste Completo:**
   - Usar `SessionTestUtils.testCompleteSessionBehavior()` no console
   - Observar todos os cenários automaticamente

## Constantes Configuráveis

```javascript
const INACTIVITY_LIMIT_DAYS = 30; // Dias de inatividade antes de expirar
const ACTIVITY_THROTTLE_MS = 30000; // Intervalo mínimo entre atualizações de atividade
```

## Benefícios

- ✅ Segurança aprimorada (logout automático em novas janelas se não quiser lembrar)
- ✅ Experiência do usuário melhorada (opção de manter conectado funciona)
- ✅ Controle de inatividade (logout após 30 dias sem uso)
- ✅ Performance otimizada (throttling de atualizações de atividade)
- ✅ Compatibilidade mantida (funciona com sistema existente)

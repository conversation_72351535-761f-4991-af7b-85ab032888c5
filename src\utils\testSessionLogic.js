/**
 * Teste da lógica de sessão corrigida
 */

console.log('🧪 Testando nova lógica de sessão...');

// Limpar tudo primeiro
localStorage.clear();
sessionStorage.clear();

console.log('\n=== CENÁRIO 1: Login SEM "lembrar dispositivo" ===');

// 1. Simular login SEM lembrar dispositivo
localStorage.setItem('rememberDevice', 'false');  // Preferência sempre no localStorage
sessionStorage.setItem('isAuthenticated', 'true'); // Auth no sessionStorage

console.log('1. Após login SEM lembrar dispositivo:');
console.log('   - localStorage.rememberDevice:', localStorage.getItem('rememberDevice'));
console.log('   - localStorage.isAuthenticated:', localStorage.getItem('isAuthenticated'));
console.log('   - sessionStorage.isAuthenticated:', sessionStorage.getItem('isAuthenticated'));

// 2. Verificar lógica de nova sessão
const hasLocalAuth = localStorage.getItem('isAuthenticated') === 'true';
const hasSessionAuth = sessionStorage.getItem('isAuthenticated') === 'true';
const rememberDevice = localStorage.getItem('rememberDevice') === 'true';

console.log('\n2. Verificação de nova sessão:');
console.log('   - rememberDevice:', rememberDevice);
console.log('   - hasLocalAuth:', hasLocalAuth);
console.log('   - hasSessionAuth:', hasSessionAuth);
console.log('   - isNewBrowserSession:', !rememberDevice && hasLocalAuth && !hasSessionAuth);
console.log('   - Resultado: DEVE MANTER LOGADO (hasSessionAuth = true)');

// 3. Simular nova sessão do navegador (sessionStorage limpo)
sessionStorage.clear();
const hasLocalAuth2 = localStorage.getItem('isAuthenticated') === 'true';
const hasSessionAuth2 = sessionStorage.getItem('isAuthenticated') === 'true';

console.log('\n3. Após nova sessão do navegador (sessionStorage limpo):');
console.log('   - hasLocalAuth:', hasLocalAuth2);
console.log('   - hasSessionAuth:', hasSessionAuth2);
console.log('   - isNewBrowserSession:', !rememberDevice && hasLocalAuth2 && !hasSessionAuth2);
console.log('   - Resultado: DEVE DESLOGAR (hasLocalAuth=false, hasSessionAuth=false)');

console.log('\n=== CENÁRIO 2: Login COM "lembrar dispositivo" ===');

// Limpar e testar COM lembrar dispositivo
localStorage.clear();
sessionStorage.clear();

localStorage.setItem('rememberDevice', 'true');
localStorage.setItem('isAuthenticated', 'true');

const rememberDevice2 = localStorage.getItem('rememberDevice') === 'true';
console.log('4. Com lembrar dispositivo:');
console.log('   - rememberDevice:', rememberDevice2);
console.log('   - isNewBrowserSession:', false); // Sempre false quando rememberDevice = true

console.log('\n✅ Nova lógica testada!');

import { onMounted, onUnmounted } from 'vue'
import usuariosService from '@/services/usuariosService'

/**
 * Composable para rastrear atividade do usuário
 * Atualiza o timestamp de última atividade quando o usuário interage com a aplicação
 */
export function useActivityTracker() {
  let activityTimeout = null
  const ACTIVITY_THROTTLE_MS = 30000 // Atualizar no máximo a cada 30 segundos

  // Eventos que indicam atividade do usuário
  const activityEvents = [
    'mousedown',
    'mousemove', 
    'keypress',
    'scroll',
    'touchstart',
    'click'
  ]

  // Função throttled para atualizar atividade
  const updateActivity = () => {
    if (activityTimeout) return // Já há um timeout ativo

    // Verificar se o usuário está autenticado antes de atualizar
    if (usuariosService.isAuthenticated()) {
      usuariosService.SessionManager.updateActivity()
    }

    // Throttle: próxima atualização só após o intervalo definido
    activityTimeout = setTimeout(() => {
      activityTimeout = null
    }, ACTIVITY_THROTTLE_MS)
  }

  // Adicionar listeners de atividade
  const addActivityListeners = () => {
    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true })
    })
  }

  // Remover listeners de atividade
  const removeActivityListeners = () => {
    activityEvents.forEach(event => {
      document.removeEventListener(event, updateActivity)
    })
  }

  // Inicializar quando o componente for montado
  onMounted(() => {
    addActivityListeners()
  })

  // Limpar quando o componente for desmontado
  onUnmounted(() => {
    removeActivityListeners()
    if (activityTimeout) {
      clearTimeout(activityTimeout)
    }
  })

  return {
    updateActivity,
    addActivityListeners,
    removeActivityListeners
  }
}

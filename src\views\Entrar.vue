<template>
  <div class="page-header align-items-start min-vh-100" v-bind:style="backgroundImage" :class="{ 'breathing': showAnimations }">
    <!-- Background animado -->
    <span class="mask bg-gradient-dark opacity-6" :class="{ 'animated-mask': showAnimations }"></span>

    <!-- Login submission loading overlay -->
    <div
      v-if="isLoggingIn"
      class="login-loading-overlay"
      :class="{ 'show': isLoggingIn }"
    ></div>

    <div class="container my-auto" :class="{ 'animated-container': showAnimations }">
      <div class="row">
        <div class="col-lg-4 col-md-8 col-12 mx-auto">
          <div class="card z-index-0 login-card" :class="{ 'logging-in': isLoggingIn, 'animated-card': showAnimations }">
            <!-- Header do card com logo -->
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2" :class="{ 'animated-header': showAnimations }">
              <div
                class="shadow-secondary border-radius-lg py-3 bg-gradient-lumi"
                style="border: 1px solid #d2d2d2; padding-left: 35px"
              >
                <img :src="LumiBlueLogo" class="login-page-logo" :class="{ 'animated-logo': showAnimations }" />
              </div>
            </div>

            <!-- Corpo do card com formulário -->
            <div class="card-body" :class="{ 'animated-body': showAnimations }">
              <form role="form" class="text-start mt-3" @submit.prevent="submitLogin">
                <!-- Campo usuário -->
                <div class="mb-3" :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.4s' : ''">
                  <MaterialInput
                    id="username"
                    type="text"
                    label="Usuário"
                    name="username"
                    v-model="credentials.username"
                  />
                </div>

                <!-- Campo senha -->
                <div class="mb-3" :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.5s' : ''">
                  <MaterialInput
                    id="senha"
                    type="password"
                    label="Senha"
                    v-model="credentials.password"
                    name="senha"
                  />
                </div>

                <!-- Switch lembrar dispositivo -->
                <div :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.6s' : ''">
                  <material-switch
                    id="rememberMe"
                    name="rememberMe"
                    :checked="rememberDevice"
                    @change="rememberDevice = $event"
                    >Manter este dispositivo conectado</material-switch
                  >
                </div>

                <!-- Botão de login -->
                <div class="text-center" :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.7s' : ''">
                  <material-button
                    class="my-4 mb-2"
                    variant="gradient"
                    color="secondary"
                    fullWidth
                    :loading="isLoggingIn"
                    :loadingText="$t('login.loggingIn')"
                  >
                    {{ $t("login.submitAction") }}
                  </material-button>
                </div>

                <!-- Link para conhecer -->
                <div class="mt-4 text-sm text-center w-100" :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.8s' : ''">
                  <a
                    href="https://lumiorthosystem.com.br"
                    class="text-decoration-none"
                    target="_blank"
                  >
                    <div>Ainda não é cliente?</div>
                    <div><b>Conheça-nos</b></div>
                  </a>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Footer animado -->
    <footer class="footer position-absolute bottom-2 py-2 w-100" :class="{ 'animated-footer': showAnimations }">
      <div class="container">
        <div class="row align-items-center justify-content-lg-between">
          <div class="col-12 my-auto">
            <div
              class="copyright text-center text-sm text-white text-lg-start d-flex flex-column"
              style="font-weight: 400"
            >
              <span style="font-size: 11pt"
                >© {{ new Date().getFullYear() }} Lumi Plan</span
              >
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>
<style scoped>
@media (min-width: 960px) {
  .card {
    min-width: 370px;
  }
}

/* Adicionar margens laterais no mobile para evitar que o card cole nas bordas */
@media (max-width: 767.98px) {
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Garantir que o card tenha um espaçamento mínimo das bordas */
  .login-card {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
}

/* Corrigir cor do switch para azul */
:deep(.form-switch .form-check-input:checked) {
  background-color: #56809F !important;
  border-color: #56809F !important;
}

:deep(.form-switch .form-check-input:checked:after) {
  border-color: #56809F !important;
}

/* ===== ESTADOS INICIAIS (ANTES DAS ANIMAÇÕES) ===== */

/* Elementos começam invisíveis até as animações serem ativadas */
.container:not(.animated-container) {
  opacity: 0;
}

.login-card:not(.animated-card) {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
}

.card-header:not(.animated-header) {
  opacity: 0;
  transform: translateY(-20px);
}

.login-page-logo:not(.animated-logo) {
  opacity: 0;
  transform: scale(0.8);
  filter: brightness(0.8);
}

.card-body:not(.animated-body) {
  opacity: 0;
}

.footer:not(.animated-footer) {
  opacity: 0;
  transform: translateY(20px);
}

/* Campos do formulário começam invisíveis */
.mb-3:not(.animated-field),
.text-center:not(.animated-field),
.mt-4:not(.animated-field) {
  opacity: 0;
  transform: translateX(-20px);
}

/* ===== ANIMAÇÕES DE "SISTEMA ACORDANDO" ===== */

/* Animação do background - efeito de despertar */
.animated-mask {
  animation: backgroundAwaken 1.2s ease-out forwards;
}

@keyframes backgroundAwaken {
  0% {
    opacity: 0.9;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

/* Container principal - movimento suave de entrada */
.animated-container {
  animation: containerFloat 1s ease-out forwards;
}

@keyframes containerFloat {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card principal - entrada elegante com escala */
.animated-card {
  animation: cardAwaken 0.8s ease-out 0.2s forwards;
}

@keyframes cardAwaken {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header do card - deslizamento suave */
.animated-header {
  animation: headerSlide 0.6s ease-out 0.4s forwards;
  opacity: 0;
  transform: translateY(-20px);
}

@keyframes headerSlide {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo - efeito de "despertar" com brilho */
.animated-logo {
  animation: logoAwaken 0.8s ease-out 0.5s forwards;
  opacity: 0;
  transform: scale(0.8);
  filter: brightness(0.8);
}

@keyframes logoAwaken {
  0% {
    opacity: 0;
    transform: scale(0.8);
    filter: brightness(0.8);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
    filter: brightness(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1);
  }
}

/* Corpo do card - fade in suave */
.animated-body {
  animation: bodyFadeIn 0.6s ease-out 0.6s forwards;
  opacity: 0;
}

@keyframes bodyFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Campos do formulário - entrada sequencial */
.animated-field {
  animation: fieldSlideIn 0.5s ease-out forwards;
  opacity: 0;
  transform: translateX(-20px);
}

@keyframes fieldSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Footer - entrada sutil de baixo */
.animated-footer {
  animation: footerRise 0.8s ease-out 0.9s forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes footerRise {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== ANIMAÇÕES DE LOADING (MANTIDAS) ===== */

/* Login submission loading overlay */
.login-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.25);
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out;
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
}

.login-loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Animações de loading */
.login-card {
  transition: all 0.3s ease;
}

.login-card.logging-in {
  transform: scale(0.98);
  opacity: 0.9;
}

.login-card.logging-in .card-body {
  pointer-events: none;
}

/* Efeito de pulse no logo durante o loading */
.login-card.logging-in .login-page-logo {
  animation: loginPulse 1.5s ease-in-out infinite;
}

@keyframes loginPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* ===== ANIMAÇÕES DE INTERAÇÃO (SISTEMA VIVO) ===== */

/* Card com hover sutil */
.login-card:not(.logging-in) {
  transition: all 0.3s ease;
}

.login-card:not(.logging-in):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Logo com hover interativo */
.login-page-logo {
  transition: all 0.3s ease;
  cursor: pointer;
}

.login-page-logo:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

/* Links com animação suave */
.animated-footer a,
.card-body a {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.animated-footer a:hover,
.card-body a:hover {
  transform: translateY(-1px);
}

/* Efeito de respiração sutil no background */
.page-header.breathing {
  animation: backgroundBreathe 8s ease-in-out infinite;
}

@keyframes backgroundBreathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.01);
  }
}

/* Animação de foco nos campos */
:deep(.form-floating > .form-control:focus),
:deep(.form-floating > .form-control:not(:placeholder-shown)) {
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

/* Responsividade das animações */
@media (max-width: 768px) {
  .animated-card {
    animation-duration: 0.6s;
  }

  .animated-field {
    animation-duration: 0.4s;
  }

  .page-header {
    animation: none; /* Desabilitar respiração em mobile */
  }
}

/* Reduzir animações para usuários que preferem menos movimento */
@media (prefers-reduced-motion: reduce) {
  .animated-mask,
  .animated-container,
  .animated-card,
  .animated-header,
  .animated-logo,
  .animated-body,
  .animated-field,
  .animated-footer,
  .page-header {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .login-card:hover {
    transform: none;
  }
}
</style>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import MaterialSwitch from "@/components/MaterialSwitch.vue";
import MaterialButton from "@/components/MaterialButton.vue";
import { mapMutations } from "vuex";
import whiteConsultory from "@/assets/img/lumi/whiteConsultory.jpg";
import LumiBlueLogo from "@/assets/img/lumi/logo-blue.png";
import usuariosService from "@/services/usuariosService.js";
import router from "../router/index.js";
import cSwal from "@/utils/cSwal.js";

const credentials = {
  username: "",
  password: "",
};

export default {
  name: "login",
  components: {
    MaterialInput,
    MaterialSwitch,
    MaterialButton,
  },
  data() {
    return {
      credentials,
      LumiBlueLogo,
      rememberDevice: false,
      isLoggingIn: false,
      showAnimations: false, // Controla quando as animações começam
    };
  },
  mounted() {
    if (usuariosService.isAuthenticated()) {
      router.push({ path: "agenda" });
      return;
    }

    // Aguardar o loading inicial terminar e então iniciar as animações
    this.waitForInitialLoadingToEnd();
  },
  methods: {
    ...mapMutations(["toggleEveryDisplay", "toggleHideConfig"]),

    // Método para aguardar o loading inicial terminar
    waitForInitialLoadingToEnd() {
      // Verificar se o loading inicial ainda existe
      const checkLoader = () => {
        const initialLoader = document.getElementById('app-initial-loader');
        if (initialLoader && initialLoader.style.display !== 'none' && !initialLoader.classList.contains('fade-out')) {
          // Loading ainda ativo, verificar novamente em 100ms
          setTimeout(checkLoader, 100);
        } else {
          // Loading terminou, aguardar um pouco mais e iniciar animações
          setTimeout(() => {
            this.showAnimations = true;
          }, 300);
        }
      };

      // Fallback: se não encontrar o loader, iniciar animações após um tempo
      setTimeout(() => {
        if (!this.showAnimations) {
          this.showAnimations = true;
        }
      }, 1500);

      // Iniciar verificação
      checkLoader();
    },

    async submitLogin() {
      if (this.isLoggingIn) return; // Previne múltiplos submits

      this.isLoggingIn = true;

      try {
        const auth = await usuariosService.login(this.credentials, this.rememberDevice);

        if (auth) {
          // Pequeno delay para mostrar o feedback visual
          setTimeout(() => {
            this.$router.go("/agenda");
          }, 500);
        } else {
          this.isLoggingIn = false;
          cSwal.cError("Usuário ou senha incorretos.");
        }
      } catch (error) {
        this.isLoggingIn = false;
        cSwal.cError("Erro ao fazer login. Tente novamente.");
      }
    },
  },
  computed: {
    backgroundImage() {
      return {
        backgroundImage: `url(${whiteConsultory})`,
        transform: "scale(1.05)",
      };
    },
  },
  beforeMount() {
    this.toggleEveryDisplay();
    this.toggleHideConfig();
  },
  beforeUnmount() {
    this.toggleEveryDisplay();
    this.toggleHideConfig();
  },
};
</script>
